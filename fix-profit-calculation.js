/**
 * ملف إصلاح مشكلة حساب وتسجيل الأرباح
 */

const path = require('path');
const Database = require('better-sqlite3');

// إعداد قاعدة البيانات
const dbPath = path.join(__dirname, 'database.db');
const db = new Database(dbPath);

// استيراد الوحدات المطلوبة
const { calculateProfit, calculateProfitWithTransport } = require('./utils/profitCalculator');

console.log('=== بدء إصلاح مشكلة الأرباح ===\n');

// 1. التحقق من وجود حقل profit في جدول transactions
console.log('1. التحقق من بنية جدول transactions...');
try {
  const tableInfo = db.prepare("PRAGMA table_info(transactions)").all();
  const profitColumn = tableInfo.find(col => col.name === 'profit');
  
  if (!profitColumn) {
    console.log('إضافة حقل profit إلى جدول transactions...');
    db.prepare('ALTER TABLE transactions ADD COLUMN profit REAL DEFAULT 0').run();
    console.log('✅ تم إضافة حقل profit بنجاح');
  } else {
    console.log('✅ حقل profit موجود بالفعل');
  }
} catch (error) {
  console.error('خطأ في التحقق من حقل profit:', error.message);
}

// 2. التحقق من وجود حقل profit_total في جدول cashbox
console.log('\n2. التحقق من بنية جدول cashbox...');
try {
  const cashboxInfo = db.prepare("PRAGMA table_info(cashbox)").all();
  const profitTotalColumn = cashboxInfo.find(col => col.name === 'profit_total');
  
  if (!profitTotalColumn) {
    console.log('إضافة حقل profit_total إلى جدول cashbox...');
    db.prepare('ALTER TABLE cashbox ADD COLUMN profit_total REAL DEFAULT 0').run();
    console.log('✅ تم إضافة حقل profit_total بنجاح');
  } else {
    console.log('✅ حقل profit_total موجود بالفعل');
  }
} catch (error) {
  console.error('خطأ في التحقق من حقل profit_total:', error.message);
}

// 3. إصلاح قيم الأرباح في المعاملات الموجودة
console.log('\n3. إصلاح قيم الأرباح في المعاملات الموجودة...');
try {
  // الحصول على جميع معاملات البيع التي لها قيم ربح خاطئة أو مفقودة
  const salesTransactions = db.prepare(`
    SELECT id, item_id, quantity, price, selling_price, total_price, profit, transport_cost
    FROM transactions 
    WHERE transaction_type = 'sale'
  `).all();
  
  console.log(`تم العثور على ${salesTransactions.length} معاملة بيع`);
  
  let fixedCount = 0;
  const updateStmt = db.prepare('UPDATE transactions SET profit = ? WHERE id = ?');
  
  salesTransactions.forEach(transaction => {
    // حساب الربح الصحيح
    let correctProfit;
    
    if (transaction.transport_cost && transaction.transport_cost > 0) {
      // حساب مصاريف النقل لكل وحدة
      const transportCostPerUnit = transaction.transport_cost / transaction.quantity;
      correctProfit = calculateProfitWithTransport(
        transaction.selling_price,
        transaction.price,
        transaction.quantity,
        transportCostPerUnit
      );
    } else {
      correctProfit = calculateProfit(
        transaction.selling_price,
        transaction.price,
        transaction.quantity
      );
    }
    
    // التحقق من أن الربح المحسوب مختلف عن المسجل
    if (Math.abs((transaction.profit || 0) - correctProfit) > 0.01) {
      updateStmt.run(correctProfit, transaction.id);
      console.log(`تم إصلاح المعاملة ${transaction.id}: الربح القديم ${transaction.profit || 0} → الربح الجديد ${correctProfit}`);
      fixedCount++;
    }
  });
  
  console.log(`✅ تم إصلاح ${fixedCount} معاملة`);
} catch (error) {
  console.error('خطأ في إصلاح قيم الأرباح:', error.message);
}

// 4. إعادة حساب إجمالي الأرباح في الخزينة
console.log('\n4. إعادة حساب إجمالي الأرباح في الخزينة...');
try {
  // حساب إجمالي الأرباح من جميع معاملات البيع
  const totalProfitResult = db.prepare(`
    SELECT COALESCE(SUM(profit), 0) as total_profit
    FROM transactions
    WHERE transaction_type = 'sale'
  `).get();
  
  const calculatedTotalProfit = totalProfitResult.total_profit;
  console.log(`إجمالي الأرباح المحسوب من المعاملات: ${calculatedTotalProfit}`);
  
  // تحديث إجمالي الأرباح في الخزينة
  const updateCashboxStmt = db.prepare(`
    UPDATE cashbox
    SET profit_total = ?,
        updated_at = ?
    WHERE id = (SELECT MIN(id) FROM cashbox)
  `);
  
  const result = updateCashboxStmt.run(calculatedTotalProfit, new Date().toISOString());
  
  if (result.changes > 0) {
    console.log(`✅ تم تحديث إجمالي الأرباح في الخزينة إلى: ${calculatedTotalProfit}`);
  } else {
    console.log('⚠️  لم يتم العثور على خزينة لتحديثها');
  }
} catch (error) {
  console.error('خطأ في تحديث إجمالي الأرباح:', error.message);
}

// 5. التحقق من النتائج النهائية
console.log('\n5. التحقق من النتائج النهائية...');
try {
  // فحص بعض المعاملات للتأكد من الإصلاح
  const sampleTransactions = db.prepare(`
    SELECT id, quantity, price, selling_price, profit, transport_cost
    FROM transactions 
    WHERE transaction_type = 'sale' 
    ORDER BY id DESC 
    LIMIT 3
  `).all();
  
  console.log('عينة من المعاملات بعد الإصلاح:');
  sampleTransactions.forEach(transaction => {
    const expectedProfit = transaction.transport_cost > 0 
      ? calculateProfitWithTransport(transaction.selling_price, transaction.price, transaction.quantity, transaction.transport_cost / transaction.quantity)
      : calculateProfit(transaction.selling_price, transaction.price, transaction.quantity);
    
    console.log(`المعاملة ${transaction.id}: الربح المسجل ${transaction.profit}, المتوقع ${expectedProfit}`);
    
    if (Math.abs(transaction.profit - expectedProfit) < 0.01) {
      console.log('  ✅ صحيح');
    } else {
      console.log('  ❌ لا يزال هناك خطأ');
    }
  });
  
  // فحص بيانات الخزينة النهائية
  const cashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();
  if (cashbox) {
    console.log('\nبيانات الخزينة النهائية:');
    console.log(`  الرصيد الحالي: ${cashbox.current_balance}`);
    console.log(`  إجمالي المبيعات: ${cashbox.sales_total}`);
    console.log(`  إجمالي المشتريات: ${cashbox.purchases_total}`);
    console.log(`  إجمالي الأرباح: ${cashbox.profit_total}`);
  }
} catch (error) {
  console.error('خطأ في التحقق من النتائج:', error.message);
}

console.log('\n=== انتهاء الإصلاح ===');
console.log('يمكنك الآن تشغيل التطبيق والتحقق من أن الأرباح تُحسب وتُسجل بشكل صحيح.');

// إغلاق قاعدة البيانات
db.close();
