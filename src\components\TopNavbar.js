import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  FaHome, FaBoxes, FaWarehouse, FaChartBar, FaUsers,
  FaSignOutAlt, FaShoppingCart, FaMoneyBillWave, FaCog,
  FaCalculator, FaUserFriends, FaBars, FaTimes,
  FaUserCircle, FaBell, FaWallet, FaCheck
} from 'react-icons/fa';
import { useApp } from '../context/AppContext';
import './TopNavbar.css';

const TopNavbar = ({ user, onLogout }) => {
  const navigate = useNavigate();
  const location = useLocation();
  // Variable no utilizada - considere eliminarla o usarla
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  // متغير لحالة قائمة المستخدم
  const [userMenuOpen, setUserMenuOpen] = useState(false);

  // استخراج إعدادات النظام من سياق التطبيق
  const { settings } = useApp();

  const menuItems = [
    { path: '/', icon: <FaHome />, text: 'الرئيسية' },
    { path: '/items', icon: <FaBoxes />, text: 'الأصناف' },
    { path: '/inventory', icon: <FaWarehouse />, text: 'المخزون' },
    { path: '/purchases', icon: <FaShoppingCart />, text: 'المشتريات' },
    { path: '/customers', icon: <FaUserFriends />, text: 'العميل' },
    { path: '/cashbox', icon: <FaWallet />, text: 'الخزينة' },
    { path: '/reports', icon: <FaChartBar />, text: 'التقارير' },
    { path: '/zakat', icon: <FaCalculator />, text: 'الزكاة' },
    { path: '/users', icon: <FaUsers />, text: 'المستخدمين', adminOnly: true },
    { path: '/settings', icon: <FaCog />, text: 'الإعدادات' },
  ];

  const handleNavigation = (path) => {
    navigate(path);
    setMobileMenuOpen(false);
    setUserMenuOpen(false);
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
    setUserMenuOpen(false);
  };

  const toggleUserMenu = () => {
    setUserMenuOpen(!userMenuOpen);
  };

  // تم إزالة كل الكود المتعلق بالإشعارات

  return (
    <>
      <div className="top-navbar">
        <div className="top-navbar-container">
          {/* زر القائمة للشاشات الصغيرة */}
          <div className="mobile-menu-toggle" onClick={toggleMobileMenu}>
            {mobileMenuOpen ? <FaTimes /> : <FaBars />}
          </div>

          {/* الشعار واسم النظام */}
          <div className="top-navbar-brand" onClick={() => handleNavigation('/')}>
            <div className="top-logo">
              {settings.logoUrl ? (
                <img
                  src={settings.logoUrl}
                  alt="شعار النظام"
                  className="logo-image"
                />
              ) : (
                <span className="logo-text">Hgorp</span>
              )}
            </div>
            <h3>{settings.systemName || 'نظام إدارة المخازن'}</h3>
          </div>

          {/* عناصر القائمة الرئيسية */}
          <div className={`top-navbar-menu ${mobileMenuOpen ? 'mobile-open' : ''}`}>
            {menuItems.map((item) => {
              // إذا كان العنصر مخصص للمسؤولين فقط وليس المستخدم مسؤولاً، نتخطى هذا العنصر
              if (item.adminOnly && user?.role !== 'admin' && user?.role !== 'manager') {
                return null;
              }

              return (
                <div
                  key={item.path}
                  className={`top-navbar-menu-item ${location.pathname === item.path ? 'active' : ''}`}
                  onClick={() => handleNavigation(item.path)}
                >
                  <div className="menu-icon">{item.icon}</div>
                  <span className="menu-text">{item.text}</span>
                </div>
              );
            })}
          </div>

          {/* قسم المستخدم والإشعارات */}
          <div className="top-navbar-actions">
            {/* تم إخفاء زر الإشعارات */}

            {/* معلومات المستخدم */}
            <div className="user-profile" onClick={toggleUserMenu}>
              <FaUserCircle className="user-avatar" />
              <span className="user-name">{user?.username || 'المستخدم'}</span>
            </div>

            {/* قائمة المستخدم المنسدلة */}
            {userMenuOpen && (
              <div className="user-dropdown">
                <div className="user-dropdown-header">
                  <FaUserCircle className="user-dropdown-avatar" />
                  <div className="user-dropdown-info">
                    <div className="user-dropdown-name">{user?.username || 'المستخدم'}</div>
                    <div className="user-dropdown-role">{user?.role === 'admin' ? 'مدير النظام' : 'مستخدم'}</div>
                  </div>
                </div>
                <div className="user-dropdown-divider"></div>
                <div className="user-dropdown-item" onClick={() => handleNavigation('/settings')}>
                  <FaCog className="user-dropdown-icon" />
                  <span>الإعدادات</span>
                </div>
                <div className="user-dropdown-item logout" onClick={onLogout}>
                  <FaSignOutAlt className="user-dropdown-icon" />
                  <span>تسجيل الخروج</span>
                </div>
              </div>
            )}

            {/* تم إخفاء قائمة الإشعارات المنسدلة */}
          </div>
        </div>
      </div>

      {/* مساحة فارغة لتعويض ارتفاع شريط التنقل */}
      <div className="top-navbar-spacer"></div>
    </>
  );
};

export default TopNavbar;
